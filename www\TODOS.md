# TODOS - TrainerBattleScreen Bugfixes

## Projektübersicht
Behebung kritischer Bugs im TrainerBattleScreen für korrekte Funktionalität und Benutzerfreundlichkeit.

## Prioritäten
- 🔴 **Hoch** - Kritische Bugs die sofort behoben werden müssen
- 🟡 **Mittel** - Wichtige Verbesserungen für bessere UX
- 🟢 **Niedrig** - Feinschliff und Optimierungen

## 🐛 Kritische Bugfixes

### 🔴 Hoch
- [x] **Battle-Intro Screen entfernen** ✅ **BEHOBEN**
  - **Problem**: Überflüssiger battle-intro Screen mit NPC Trainer Bild und Farbverlauf
  - **Lösung**:
    - `#battle-intro` div und zugehöriges CSS aus TrainerBattleScreen.js entfernt ✅
    - `battle-intro` Klassen aus trainer-battle.css entfernt ✅
    - `showBattleIntro()` Methode vereinfacht - startet direkt Battle ✅
    - Direkt zu `startActualBattle()` ohne Intro-Screen ✅
  - **Dateien**: TrainerBattleScreen.js, trainer-battle.css
  - **Akzeptanzkriterien**: Kein separater Intro-Screen, direkter Start des Kampfes ✅

- [x] **XP-System für Trainer-Kämpfe korrigieren** ✅ **BEHOBEN**
  - **Problem**: XP wird nur als Zahl erhöht, aber XP-Bar wird nicht animiert und kein Level-up Hinweis
  - **Lösung**:
    - `updateExpBar()` Methode aus battle-utils.js in `awardRoundXP()` verwendet ✅
    - XP-Bar Animation nach jedem Rundensieg implementiert ✅
    - Level-up Notification wie in BattleScreen.js implementiert ✅
    - `showLevelUpNotification()` korrekt aufgerufen ✅
  - **Dateien**: TrainerBattleScreen.js, battle-utils.js
  - **Abhängigkeiten**: battle-utils.js korrekt importiert ✅
  - **Akzeptanzkriterien**: XP-Bar animiert sich, Level-up wird angezeigt ✅

- [x] **XP-Persistierung nach jeder Runde** ✅ **BEHOBEN**
  - **Problem**: XP wird nur gespeichert wenn gesamter Trainerkampf gewonnen wird
  - **Lösung**:
    - `pokemonManager.updatePokemon()` nach jeder gewonnenen Runde aufgerufen ✅
    - `pokemonManager.saveTeam()` nach jedem XP-Gewinn ausgeführt ✅
    - XP-Speicherung von Kampfende entkoppelt ✅
    - In `awardRoundXP()` sofortige Speicherung implementiert ✅
  - **Dateien**: TrainerBattleScreen.js, pokemon-manager.js
  - **Akzeptanzkriterien**: XP bleibt auch bei Kampfniederlage erhalten ✅

### 🔴 Hoch
- [x] **FAB-Submenu während Trainerkampf ausblenden** ✅ **BEHOBEN**
  - **Problem**: Submenu bleibt sichtbar wenn Trainerkampf gestartet wird
  - **Lösung**:
    - `fabSubmenuManager.closeSubmenu()` in `show()` Methode aufgerufen ✅
    - Dynamischen Import wie in `hide()` Methode verwendet ✅
    - Submenu vor FAB-Ausblendung geschlossen ✅
  - **Code implementiert**:
    ```javascript
    // In show() Methode vor fabManager.hideAllButtons():
    import('../ui/FabSubmenuManager.js').then(({ fabSubmenuManager }) => {
        if (fabSubmenuManager.isActive) {
            fabSubmenuManager.closeSubmenu();
        }
    }).catch(e => {
        logger.warn('Could not import fabSubmenuManager:', e);
    });
    ```
  - **Dateien**: TrainerBattleScreen.js, FabSubmenuManager.js
  - **Akzeptanzkriterien**: Submenu wird vor Kampfstart geschlossen ✅

## 🎬 Animation & Timing Verbesserungen

### 🟡 Mittel
- [x] **Kampf-Timing optimieren** ✅ **BEHOBEN**
  - **Problem**: Pausen zwischen Animationen zu lang (aktuell überall 2-3 Sekunden)
  - **Lösung**:
    - `showTrainerIntroAnimation()`: 3 Sekunden beibehalten ✅
    - `showRoundStartAnimation()`: von 1000ms auf 500ms reduziert ✅
    - `showBattleCalculationAnimation()`: von 2000ms auf 1000ms reduziert ✅
    - `showPokemonDefeatAnimation()`: von 1500ms auf 1000ms reduziert ✅
    - `showPokemonSwitchAnimation()`: 500ms beibehalten ✅
  - **Dateien**: TrainerBattleScreen.js
  - **Akzeptanzkriterien**: Flüssigere Kampfabläufe ohne lange Wartezeiten ✅

- [ ] **Trainer-Sprite Positionierung korrigieren**
  - **Problem**: Trainer-Sprites erscheinen nebeneinander statt an Pokemon-Positionen
  - **Lösung**:
    - Player-Trainer-Sprite in `.trainer-player-side` Container positionieren
    - Opponent-Trainer-Sprite in `.trainer-opponent-side` Container positionieren
    - CSS-Klassen `.player-trainer-sprite` und `.opponent-trainer-sprite` anpassen
    - Sprites an gleichen Positionen wie Pokemon-Images platzieren
  - **Dateien**: TrainerBattleScreen.js, trainer-battle.css
  - **Akzeptanzkriterien**: Trainer-Sprites an Pokemon-Positionen

## 🔧 Code-Refactoring

### 🟡 Mittel
- [ ] **XP-System Utilities verwenden**
  - **Beschreibung**: Konsistente Verwendung der battle-utils.js Funktionen
  - **Details**:
    - `updateExpBar()` für XP-Bar Updates verwenden
    - `showNotification()` für Level-up Benachrichtigungen
    - `getPokemonDisplayName()` für einheitliche Namensanzeige
  - **Akzeptanzkriterien**: Gleiche XP-Funktionalität wie BattleScreen.js
  - **Abhängigkeiten**: XP-System muss korrekt funktionieren

- [ ] **Import-Struktur optimieren**
  - **Beschreibung**: Dynamische Imports für FAB-Manager konsistent verwenden
  - **Details**:
    - Alle FAB-Manager Imports dynamisch laden
    - Error-Handling für fehlgeschlagene Imports
    - Konsistente Import-Struktur in show() und hide()
  - **Akzeptanzkriterien**: Keine Circular Dependency Probleme
  - **Abhängigkeiten**: FAB-Submenu Bug muss behoben sein

## 🎯 Validierung & Testing

### 🟢 Niedrig
- [ ] **Kampfablauf-Validierung**
  - **Beschreibung**: Sicherstellen dass alle Animationen korrekt ablaufen
  - **Details**:
    - Trainer-Intro → Pokemon-Erscheinen → Kampf → Ergebnis
    - XP-Vergabe nach jeder Runde testen
    - Level-up Animationen validieren
  - **Akzeptanzkriterien**: Flüssiger Kampfablauf ohne Unterbrechungen
  - **Abhängigkeiten**: Alle anderen Fixes müssen implementiert sein

- [ ] **Memory-Leak Prävention**
  - **Beschreibung**: Event-Listener und Timeouts korrekt aufräumen
  - **Details**:
    - Alle setTimeout/setInterval in hide() clearen
    - Event-Listener entfernen
    - Animation-Klassen nach Verwendung entfernen
  - **Akzeptanzkriterien**: Keine Memory-Leaks bei wiederholten Kämpfen
  - **Abhängigkeiten**: Animation-Timing muss optimiert sein

## Technische Spezifikationen
- **Hauptdateien**: TrainerBattleScreen.js, trainer-battle.css
- **Abhängigkeiten**: battle-utils.js, pokemon-manager.js, FabManager.js, FabSubmenuManager.js
- **Kompatibilität**: Muss mit bestehendem BattleScreen.js kompatibel bleiben

## Abgeschlossene Aufgaben
- [x] **FAB-Manager Integration**: Dynamische Imports implementiert
- [x] **Grundlegende Kampf-Logik**: Battle-Session funktioniert

## Notizen & Debugging-Hinweise
- **XP-Problem**: Überprüfe `awardRoundXP()` - möglicherweise wird `updatePokemon()` nicht aufgerufen
- **Submenu-Problem**: `fabSubmenuManager.isActive` Status vor Kampfstart prüfen
- **Timing-Problem**: Alle `setTimeout` Werte in TrainerBattleScreen.js reduzieren

## Ressourcen
- BattleScreen.js als Referenz für XP-System
- battle-utils.js für einheitliche Funktionen
- FabManager.js für korrekte FAB-Behandlung
- Überprüfe unbedingt auch weitere Ressourcen, falls Verknüpfungen dazu bestehen!
- Die Lösungen sind nur Vorschläge, prüfe immer die Dateien und ggf weitere Dateien die nicht erwähnt wurden, aber Verbindungen haben. Entwickle die Lösung selbst und bleibe unserem Code-Style und Kommentierungs-Style treu!