# TODOS - TrainerBattleScreen Bugfixes

## Projektübersicht
Behebung kritischer Bugs im TrainerBattleScreen für korrekte Funktionalität und Benutzerfreundlichkeit.

## Prioritäten
- 🔴 **Hoch** - Kritische Bugs die sofort behoben werden müssen
- 🟡 **Mittel** - Wichtige Verbesserungen für bessere UX
- 🟢 **Niedrig** - Feinschliff und Optimierungen

## 🐛 Kritische Bugfixes

### 🔴 Hoch
- [ ] **Battle-Intro Screen entfernen**
  - **Problem**: Überflüssiger battle-intro Screen mit NPC Trainer Bild und Farbverlauf
  - **Lösung**: 
    - `#battle-intro` div und zugehöriges CSS aus TrainerBattleScreen.js entfernen
    - `battle-intro` Klassen aus trainer-battle.css entfernen
    - `showBattleIntro()` Methode vereinfachen oder entfernen
    - Direkt zu `startActualBattle()` wechseln ohne Intro-Screen
  - **Dateien**: TrainerBattleScreen.js, trainer-battle.css
  - **Akzeptanzkriterien**: <PERSON><PERSON> separater Intro-Screen, direkter Start des Kampfes

- [ ] **XP-System für Trainer-Kämpfe korrigieren**
  - **Problem**: XP wird nur als Zahl erhöht, aber XP-Bar wird nicht animiert und kein Level-up Hinweis
  - **Lösung**:
    - `updateExpBar()` Methode aus battle-utils.js in `awardRoundXP()` verwenden
    - XP-Bar Animation nach jedem Rundensieg triggern
    - Level-up Notification wie in BattleScreen.js implementieren
    - `showLevelUpNotification()` korrekt aufrufen
  - **Dateien**: TrainerBattleScreen.js, battle-utils.js
  - **Abhängigkeiten**: battle-utils.js muss korrekt importiert sein
  - **Akzeptanzkriterien**: XP-Bar animiert sich, Level-up wird angezeigt

- [ ] **XP-Persistierung nach jeder Runde**
  - **Problem**: XP wird nur gespeichert wenn gesamter Trainerkampf gewonnen wird
  - **Lösung**:
    - `pokemonManager.updatePokemon()` nach jeder gewonnenen Runde aufrufen
    - `pokemonManager.saveTeam()` nach jedem XP-Gewinn ausführen
    - XP-Speicherung von Kampfende entkoppeln
    - In `awardRoundXP()` sofortige Speicherung implementieren
  - **Dateien**: TrainerBattleScreen.js, pokemon-manager.js
  - **Akzeptanzkriterien**: XP bleibt auch bei Kampfniederlage erhalten

### 🔴 Hoch
- [ ] **FAB-Submenu während Trainerkampf ausblenden**
  - **Problem**: Submenu bleibt sichtbar wenn Trainerkampf gestartet wird
  - **Lösung**:
    - `fabSubmenuManager.closeSubmenu()` in `show()` Methode aufrufen
    - Dynamischen Import wie in `hide()` Methode verwenden
    - Submenu vor FAB-Ausblendung schließen
  - **Code-Beispiel**:
    ```
    // In show() Methode vor fabManager.hideAllButtons():
    import('../ui/FabSubmenuManager.js').then(({ fabSubmenuManager }) => {
        if (fabSubmenuManager.isActive) {
            fabSubmenuManager.closeSubmenu();
        }
    }).catch(e => {
        logger.warn('Could not import fabSubmenuManager:', e);
    });
    ```
  - **Dateien**: TrainerBattleScreen.js, FabSubmenuManager.js
  - **Akzeptanzkriterien**: Submenu wird vor Kampfstart geschlossen

## 🎬 Animation & Timing Verbesserungen

### 🟡 Mittel
- [ ] **Kampf-Timing optimieren**
  - **Problem**: Pausen zwischen Animationen zu lang (aktuell überall 2-3 Sekunden)
  - **Lösung**:
    - `showTrainerIntroAnimation()`: 3 Sekunden beibehalten
    - `showRoundStartAnimation()`: von 1000ms auf 500ms reduzieren
    - `showBattleCalculationAnimation()`: von 2000ms auf 1000ms reduzieren
    - `showPokemonDefeatAnimation()`: von 1500ms auf 1000ms reduzieren
    - `showPokemonSwitchAnimation()`: von 500ms beibehalten
  - **Dateien**: TrainerBattleScreen.js
  - **Akzeptanzkriterien**: Flüssigere Kampfabläufe ohne lange Wartezeiten

- [ ] **Trainer-Sprite Positionierung korrigieren**
  - **Problem**: Trainer-Sprites erscheinen nebeneinander statt an Pokemon-Positionen
  - **Lösung**:
    - Player-Trainer-Sprite in `.trainer-player-side` Container positionieren
    - Opponent-Trainer-Sprite in `.trainer-opponent-side` Container positionieren
    - CSS-Klassen `.player-trainer-sprite` und `.opponent-trainer-sprite` anpassen
    - Sprites an gleichen Positionen wie Pokemon-Images platzieren
  - **Dateien**: TrainerBattleScreen.js, trainer-battle.css
  - **Akzeptanzkriterien**: Trainer-Sprites an Pokemon-Positionen

## 🔧 Code-Refactoring

### 🟡 Mittel
- [ ] **XP-System Utilities verwenden**
  - **Beschreibung**: Konsistente Verwendung der battle-utils.js Funktionen
  - **Details**:
    - `updateExpBar()` für XP-Bar Updates verwenden
    - `showNotification()` für Level-up Benachrichtigungen
    - `getPokemonDisplayName()` für einheitliche Namensanzeige
  - **Akzeptanzkriterien**: Gleiche XP-Funktionalität wie BattleScreen.js
  - **Abhängigkeiten**: XP-System muss korrekt funktionieren

- [ ] **Import-Struktur optimieren**
  - **Beschreibung**: Dynamische Imports für FAB-Manager konsistent verwenden
  - **Details**:
    - Alle FAB-Manager Imports dynamisch laden
    - Error-Handling für fehlgeschlagene Imports
    - Konsistente Import-Struktur in show() und hide()
  - **Akzeptanzkriterien**: Keine Circular Dependency Probleme
  - **Abhängigkeiten**: FAB-Submenu Bug muss behoben sein

## 🎯 Validierung & Testing

### 🟢 Niedrig
- [ ] **Kampfablauf-Validierung**
  - **Beschreibung**: Sicherstellen dass alle Animationen korrekt ablaufen
  - **Details**:
    - Trainer-Intro → Pokemon-Erscheinen → Kampf → Ergebnis
    - XP-Vergabe nach jeder Runde testen
    - Level-up Animationen validieren
  - **Akzeptanzkriterien**: Flüssiger Kampfablauf ohne Unterbrechungen
  - **Abhängigkeiten**: Alle anderen Fixes müssen implementiert sein

- [ ] **Memory-Leak Prävention**
  - **Beschreibung**: Event-Listener und Timeouts korrekt aufräumen
  - **Details**:
    - Alle setTimeout/setInterval in hide() clearen
    - Event-Listener entfernen
    - Animation-Klassen nach Verwendung entfernen
  - **Akzeptanzkriterien**: Keine Memory-Leaks bei wiederholten Kämpfen
  - **Abhängigkeiten**: Animation-Timing muss optimiert sein

## Technische Spezifikationen
- **Hauptdateien**: TrainerBattleScreen.js, trainer-battle.css
- **Abhängigkeiten**: battle-utils.js, pokemon-manager.js, FabManager.js, FabSubmenuManager.js
- **Kompatibilität**: Muss mit bestehendem BattleScreen.js kompatibel bleiben

## Abgeschlossene Aufgaben
- [x] **FAB-Manager Integration**: Dynamische Imports implementiert
- [x] **Grundlegende Kampf-Logik**: Battle-Session funktioniert

## Notizen & Debugging-Hinweise
- **XP-Problem**: Überprüfe `awardRoundXP()` - möglicherweise wird `updatePokemon()` nicht aufgerufen
- **Submenu-Problem**: `fabSubmenuManager.isActive` Status vor Kampfstart prüfen
- **Timing-Problem**: Alle `setTimeout` Werte in TrainerBattleScreen.js reduzieren
- **Sprite-Position**: CSS Grid-Layout für korrekte Positionierung verwenden

## Ressourcen
- BattleScreen.js als Referenz für XP-System
- battle-utils.js für einheitliche Funktionen
- FabManager.js für korrekte FAB-Behandlung
- Überprüfe unbedingt auch weitere Ressourcen, falls Verknüpfungen dazu bestehen!