// ui/TrainerBattleScreen.js
// Screen for trainer battles

import { logger } from '../utils/logger.js';
import { gameState } from '../state/game-state.js';
import { pokemonManager } from '../services/pokemon-manager.js';
import { BattleSession } from '../services/battle-session.js';
import { registerBackButtonHandler } from '../capacitor/app.js';
import { calculateBattleOutcome } from '../services/battle-calc.js';
import { getExpFromBattle } from '../services/experience-system.js';
import {
  updateTypeBadges,
  updateExpBar,
  updateHealthBar,
  generatePokemonIndicators,
  getPokemonDisplayName,
  getPokemonImageUrl,
  showNotification
} from '../utils/battle-utils.js';

export class TrainerBattleScreen {
    constructor() {
        this.container = null;
        this.currentBattleSession = null;
        this.backButtonHandler = null;
        this.cssLoaded = false;
    }

    /**
     * Load CSS for trainer battle screen
     */
    async loadCSS() {
        if (this.cssLoaded) return;

        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = './styles/trainer-battle.css';
            link.onload = () => {
                this.cssLoaded = true;
                resolve();
            };
            link.onerror = () => reject(new Error('Failed to load trainer battle CSS'));
            document.head.appendChild(link);
        });
    }

    /**
     * Check if player has exactly 6 Pokemon in team
     * @returns {Promise<boolean>} - Whether player has 6 Pokemon
     */
    async checkPlayerTeamRequirement() {
        try {
            await pokemonManager.initialize();
            const playerTeam = pokemonManager.getTeamPokemon();
            return playerTeam && playerTeam.length === 6;
        } catch (e) {
            logger.error('Error checking player team requirement:', e);
            return false;
        }
    }

    /**
     * Start a trainer battle
     * @param {Object} npcTrainer - The NPC trainer to battle
     */
    async startBattle(npcTrainer) {
        try {
            logger.info(`Starting trainer battle against ${npcTrainer.name}`);

            // Load CSS first
            await this.loadCSS();

            // Initialize Pokemon manager
            await pokemonManager.initialize();

            // Check if player has exactly 6 Pokemon
            const hasRequiredTeam = await this.checkPlayerTeamRequirement();
            if (!hasRequiredTeam) {
                alert('Du brauchst genau 6 Pokémon in deinem Team für einen Trainerkampf! Gehe zum Team-Bildschirm und stelle dein Team zusammen.');
                return;
            }

            // Check if NPC trainer has exactly 6 Pokemon
            if (!npcTrainer.team || npcTrainer.team.length !== 6) {
                alert('Der Trainer hat kein vollständiges Team! Kampf kann nicht gestartet werden.');
                return;
            }

            // Get player's team
            const playerTeam = pokemonManager.getTeamPokemon();

            // Create player trainer object
            const playerTrainer = {
                name: 'Spieler',
                team: playerTeam
            };

            // Show battle screen
            this.show();

            // Show battle intro with trainer sprite
            this.showBattleIntro(npcTrainer, playerTrainer);

        } catch (e) {
            logger.error('Error starting trainer battle:', e);
            alert('Fehler beim Starten des Trainerkampfs: ' + e.message);
            this.hide();
        }
    }

    /**
     * Show the trainer battle screen
     */
    show() {
        // Hide other screens
        const screens = ['map-container', 'pokemon-screen', 'team-screen', 'battle-screen'];
        screens.forEach(screenId => {
            const screen = document.getElementById(screenId);
            if (screen) screen.style.display = 'none';
        });

        // Close FAB submenu before hiding FAB buttons
        import('../ui/FabSubmenuManager.js').then(({ fabSubmenuManager }) => {
            if (fabSubmenuManager.isActive) {
                fabSubmenuManager.closeSubmenu();
            }
        }).catch(e => {
            logger.warn('Could not import fabSubmenuManager:', e);
        });

        // Hide FAB buttons during trainer battle
        const fabContainer = document.getElementById('fab-container');
        if (fabContainer) {
            fabContainer.style.display = 'none';
        }

        // Create battle screen if it doesn't exist
        if (!this.container) {
            this.createBattleScreen();
        }

        this.container.style.display = 'block';

        // Register back button handler
        this.backButtonHandler = registerBackButtonHandler(() => {
            this.hide();
        });
    }

    /**
     * Hide the trainer battle screen
     */
    hide() {
        if (this.container) {
            this.container.style.display = 'none';
        }

        // Show map
        const mapContainer = document.getElementById('map-container');
        if (mapContainer) mapContainer.style.display = 'block';

        // Show FAB buttons again
        const fabContainer = document.getElementById('fab-container');
        if (fabContainer) {
            fabContainer.style.display = 'block';
        }

        // Unregister back button handler
        if (this.backButtonHandler) {
            this.backButtonHandler(); // Call the cleanup function
            this.backButtonHandler = null;
        }

        // Clean up battle session
        this.currentBattleSession = null;

        // Show FAB buttons again
        import('../ui/FabManager.js').then(({ fabManager }) => {
            fabManager.showAllButtons();
        }).catch(e => {
            logger.warn('Could not import fabManager:', e);
        });
    }

    /**
     * Create the battle screen HTML
     */
    createBattleScreen() {
        this.container = document.createElement('div');
        this.container.id = 'trainer-battle-screen';

        this.container.innerHTML = `
            <!-- Battle Screen - BattleScreen structure -->
            <div id="battle-main" class="battle-main" style="display: none;">
                <div class="screen-header battle-header">
                    <button class="back-btn" id="trainer-battle-main-back-btn" aria-label="Zurück">
                        <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
                    </button>
                    <h1>Trainerkampf</h1>
                    <div class="header-right"></div>
                </div>

                <div class="trainer-battle-container">
                    <div class="trainer-battle-arena">
                        <!-- Info cards container at the top -->
                        <div class="trainer-battle-info-container">
                            <div class="pokemon-battle-card trainer-player-card">
                                <div class="pokemon-battle-header">
                                    <div class="pokemon-battle-name" id="player-pokemon-name">Spieler Pokemon</div>
                                    <div class="pokemon-battle-level" id="player-pokemon-level">Lvl. ?</div>
                                </div>
                                <div class="pokemon-health-container">
                                    <div class="pokemon-health-bar">
                                        <div class="pokemon-health-fill trainer-player-health-fill" style="width: 100%"></div>
                                    </div>
                                    <div class="pokemon-health-text">100%</div>
                                </div>
                                <div id="player-exp-container" class="pokemon-exp-container">
                                    <!-- XP bar will be added here -->
                                </div>
                                <div class="pokemon-battle-types" id="player-pokemon-types">
                                    <!-- Type badges will be added here -->
                                </div>
                            </div>

                            <div class="pokemon-battle-card trainer-opponent-card">
                                <div class="pokemon-battle-header">
                                    <div class="pokemon-battle-name" id="opponent-pokemon-name">Gegner Pokemon</div>
                                    <div class="pokemon-battle-level" id="opponent-pokemon-level">Lvl. ?</div>
                                </div>
                                <div class="pokemon-health-container">
                                    <div class="pokemon-health-bar">
                                        <div class="pokemon-health-fill trainer-opponent-health-fill" style="width: 100%"></div>
                                    </div>
                                    <div class="pokemon-health-text">100%</div>
                                </div>
                                <div class="pokemon-battle-types" id="opponent-pokemon-types">
                                    <!-- Type badges will be added here -->
                                </div>
                            </div>
                        </div>

                        <!-- Container with Background-Image and both sides -->
                        <div class="trainer-battle-background" style="background-image: url('./src/battleBackgrounds/bb_forest.png');">
                            <!-- Pokemon Indicators -->
                            <div class="pokemon-indicators player-indicators" id="player-indicators">
                                <!-- 6 red/gray balls for player Pokemon -->
                            </div>
                            <div class="pokemon-indicators opponent-indicators" id="opponent-indicators">
                                <!-- 6 red/gray balls for opponent Pokemon -->
                            </div>

                            <!-- Pokemon Images -->
                            <div class="trainer-opponent-side" id="trainer-opponent-side">
                                <img src="" alt="" class="pokemon-battle-image trainer-opponent-pokemon" id="opponent-pokemon-image" />
                            </div>
                            <div class="trainer-player-side" id="trainer-player-side">
                                <img src="" alt="" class="pokemon-battle-image trainer-player-pokemon" id="player-pokemon-image" />
                            </div>
                        </div>
                    </div>

                    <div class="trainer-battle-results" id="battle-results">
                        <span id="battle-result-text">Kampf läuft...</span>
                        <div class="trainer-battle-calculation" id="battle-calculation">
                            <div class="trainer-player-level" id="player-damage">?</div>
                            <div class="trainer-battle-calculation-vs">VS</div>
                            <div class="trainer-opponent-level" id="opponent-damage">?</div>
                        </div>
                    </div>

                    <!-- Continue button as FAB -->
                    <button class="fab-button trainer-continue-fab" id="trainer-continue-btn" style="display: none;">
                        <img src="./icons/materialicons/close.svg" alt="Schließen" class="icon-svg white-icon" width="24" height="24" />
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(this.container);

        // Add event listeners
        document.getElementById('trainer-battle-main-back-btn').addEventListener('click', () => this.hide());
        document.getElementById('trainer-continue-btn').addEventListener('click', () => this.hide());
    }

    /**
     * Start battle directly without intro screen
     * @param {Object} npcTrainer - The NPC trainer
     * @param {Object} playerTrainer - The player trainer
     */
    showBattleIntro(npcTrainer, playerTrainer) {
        // Import fabManager dynamically to avoid circular dependencies
        import('../ui/FabManager.js').then(({ fabManager }) => {
            // Hide all FAB buttons to prevent errors
            fabManager.hideAllButtons();
        }).catch(e => {
            logger.warn('Could not import fabManager:', e);
        });

        // Store trainers for later use
        this.npcTrainer = npcTrainer;
        this.playerTrainer = playerTrainer;

        // Start battle directly without intro screen
        this.startActualBattle();
    }

    /**
     * Start the actual battle
     */
    async startActualBattle() {
        try {
            // Show main battle screen
            document.getElementById('battle-main').style.display = 'block';

            // Show trainer intro animation (3 seconds)
            await this.showTrainerIntroAnimation();

            // Create battle session
            this.currentBattleSession = new BattleSession(this.playerTrainer, this.npcTrainer);

            // Show initial battle state
            this.updateBattleDisplay();

            // Execute battle round by round with animations
            const result = await this.executeRoundBasedBattle();

            // Show battle result
            this.showBattleResult(result);

        } catch (e) {
            logger.error('Error during battle execution:', e);
            alert('Fehler während des Kampfes: ' + e.message);
            this.hide();
        }
    }

    /**
     * Show trainer intro animation with both trainer sprites
     * @returns {Promise<void>}
     */
    async showTrainerIntroAnimation() {
        return new Promise((resolve) => {
            // Get the battle background container
            const battleBackground = document.querySelector('.trainer-battle-background');
            if (!battleBackground) {
                resolve();
                return;
            }

            // Create trainer sprites
            const playerTrainerSprite = document.createElement('img');
            playerTrainerSprite.src = './src/PlayerSprites/playerTrainer.png';
            playerTrainerSprite.className = 'trainer-intro-sprite player-trainer-sprite';
            playerTrainerSprite.alt = 'Spieler';

            const opponentTrainerSprite = document.createElement('img');
            opponentTrainerSprite.src = this.npcTrainer.getBattleSpritePath();
            opponentTrainerSprite.className = 'trainer-intro-sprite opponent-trainer-sprite';
            opponentTrainerSprite.alt = this.npcTrainer.name;

            // Create name labels
            const playerNameLabel = document.createElement('div');
            playerNameLabel.className = 'trainer-intro-name player-trainer-name';
            playerNameLabel.textContent = 'Spieler';

            const opponentNameLabel = document.createElement('div');
            opponentNameLabel.className = 'trainer-intro-name opponent-trainer-name';
            opponentNameLabel.textContent = this.npcTrainer.name;

            // Add sprites and names to the battle background
            battleBackground.appendChild(playerTrainerSprite);
            battleBackground.appendChild(opponentTrainerSprite);
            battleBackground.appendChild(playerNameLabel);
            battleBackground.appendChild(opponentNameLabel);

            // Hide Pokemon images during trainer intro
            const playerPokemonImage = document.getElementById('player-pokemon-image');
            const opponentPokemonImage = document.getElementById('opponent-pokemon-image');
            if (playerPokemonImage) playerPokemonImage.style.display = 'none';
            if (opponentPokemonImage) opponentPokemonImage.style.display = 'none';

            // After 3 seconds, animate trainers out and show Pokemon
            setTimeout(() => {
                // Add exit animations
                playerTrainerSprite.classList.add('slide-left-exit');
                opponentTrainerSprite.classList.add('slide-right-exit');
                playerNameLabel.classList.add('fade-out');
                opponentNameLabel.classList.add('fade-out');

                // After animation completes, remove trainer elements and show Pokemon
                setTimeout(() => {
                    battleBackground.removeChild(playerTrainerSprite);
                    battleBackground.removeChild(opponentTrainerSprite);
                    battleBackground.removeChild(playerNameLabel);
                    battleBackground.removeChild(opponentNameLabel);

                    // Show Pokemon images
                    if (playerPokemonImage) playerPokemonImage.style.display = 'block';
                    if (opponentPokemonImage) opponentPokemonImage.style.display = 'block';

                    resolve();
                }, 500); // Animation duration
            }, 3000); // 3 second display
        });
    }

    /**
     * Execute round-based battle with animations
     * @returns {Promise<Object>} - Battle result
     */
    async executeRoundBasedBattle() {
        const rounds = [];
        let currentRound = 0;

        // Battle continues until one trainer has no Pokemon left
        while (!this.currentBattleSession.battleEnded) {
            currentRound++;

            // Get current Pokemon for both trainers
            const playerPokemon = this.currentBattleSession.getCurrentPlayerPokemon();
            const npcPokemon = this.currentBattleSession.getCurrentNpcPokemon();

            if (!playerPokemon || !npcPokemon) {
                this.currentBattleSession.endBattle();
                break;
            }

            logger.debug(`Round ${currentRound}: ${playerPokemon.name} vs ${npcPokemon.name}`);

            // Update display for current round
            this.updateBattleDisplay();

            // Show round start animation
            await this.showRoundStartAnimation(currentRound, playerPokemon, npcPokemon);

            // Calculate battle outcome using existing battle system
            const battleResult = calculateBattleOutcome(playerPokemon, npcPokemon);

            // Show battle calculation animation
            await this.showBattleCalculationAnimation(battleResult);

            // Record the round
            const roundData = {
                round: currentRound,
                playerPokemon: { ...playerPokemon },
                npcPokemon: { ...npcPokemon },
                result: battleResult
            };
            rounds.push(roundData);

            // Process round result
            if (battleResult.playerWins) {
                // Player wins - NPC Pokemon is defeated
                this.currentBattleSession.npcPokemonStatus[this.currentBattleSession.npcCurrentPokemonIndex].defeated = true;
                logger.debug(`${npcPokemon.name} defeated by ${playerPokemon.name}`);

                // Award XP to player Pokemon for winning the round
                await this.awardRoundXP(playerPokemon, npcPokemon);

                // Show defeat animation for NPC Pokemon
                await this.showPokemonDefeatAnimation('opponent');

                // Move to next NPC Pokemon
                this.currentBattleSession.npcCurrentPokemonIndex = this.currentBattleSession.getNextActivePokemonIndex(
                    this.currentBattleSession.npcTrainer.team,
                    this.currentBattleSession.npcPokemonStatus
                );

                // Show Pokemon switch animation if there's a next Pokemon
                if (this.currentBattleSession.npcCurrentPokemonIndex !== -1) {
                    await this.showPokemonSwitchAnimation('opponent');
                }

            } else {
                // NPC wins - Player Pokemon is defeated
                this.currentBattleSession.playerPokemonStatus[this.currentBattleSession.playerCurrentPokemonIndex].defeated = true;
                logger.debug(`${playerPokemon.name} defeated by ${npcPokemon.name}`);

                // Show defeat animation for Player Pokemon
                await this.showPokemonDefeatAnimation('player');

                // Move to next Player Pokemon
                this.currentBattleSession.playerCurrentPokemonIndex = this.currentBattleSession.getNextActivePokemonIndex(
                    this.currentBattleSession.playerTrainer.team,
                    this.currentBattleSession.playerPokemonStatus
                );

                // Show Pokemon switch animation if there's a next Pokemon
                if (this.currentBattleSession.playerCurrentPokemonIndex !== -1) {
                    await this.showPokemonSwitchAnimation('player');
                }
            }

            this.currentBattleSession.currentRound++;

            // Check if battle should end
            if (this.currentBattleSession.playerCurrentPokemonIndex === -1 ||
                this.currentBattleSession.npcCurrentPokemonIndex === -1) {
                this.currentBattleSession.endBattle();
            }

            // Small delay between rounds
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Return final battle result
        const result = {
            winner: this.currentBattleSession.winner,
            playerTrainer: this.currentBattleSession.playerTrainer,
            npcTrainer: this.currentBattleSession.npcTrainer,
            rounds: rounds,
            totalRounds: currentRound
        };

        logger.info(`Battle completed. Winner: ${result.winner ? result.winner.name : 'Draw'}`);
        return result;
    }

    /**
     * Update the battle display
     */
    updateBattleDisplay() {
        if (!this.currentBattleSession) return;

        // Use requestAnimationFrame for smooth updates
        requestAnimationFrame(() => {
            const status = this.currentBattleSession.getBattleStatus();

            // Update battle result text
            const resultTextElement = document.getElementById('battle-result-text');
            if (resultTextElement) {
                if (status.battleEnded) {
                    if (status.winner) {
                        resultTextElement.innerHTML = `<strong>${status.winner.name} gewinnt!</strong>`;
                    } else {
                        resultTextElement.innerHTML = '<strong>Unentschieden!</strong>';
                    }
                } else {
                    resultTextElement.innerHTML = `Runde ${status.currentRound + 1} - Pokémon bereit!`;
                }
            }

            // Update Pokemon cards
            this.updatePokemonCard('player', status.playerCurrentPokemon, status.playerPokemonRemaining);
            this.updatePokemonCard('opponent', status.npcCurrentPokemon, status.npcPokemonRemaining);

            // Update Pokemon indicators
            this.updatePokemonIndicators();
        });
    }

    /**
     * Update Pokemon card (BattleScreen style)
     * @param {string} side - 'player' or 'opponent'
     * @param {Object} pokemon - Pokemon to display
     * @param {number} remaining - Number of Pokemon remaining
     */
    updatePokemonCard(side, pokemon, remaining) {
        if (!pokemon) return;

        // Get display name using shared utility
        const displayName = getPokemonDisplayName(pokemon);

        // Update Pokemon name and level
        const nameElement = document.getElementById(`${side}-pokemon-name`);
        const levelElement = document.getElementById(`${side}-pokemon-level`);
        const imageElement = document.getElementById(`${side}-pokemon-image`);

        if (nameElement) nameElement.textContent = displayName;
        if (levelElement) levelElement.textContent = `Lvl. ${pokemon.level || '?'}`;

        if (imageElement) {
            imageElement.src = getPokemonImageUrl(pokemon);
            imageElement.alt = displayName;
        }

        // Update type badges using shared utility
        const typesElement = document.getElementById(`${side}-pokemon-types`);
        if (typesElement) {
            updateTypeBadges(typesElement, pokemon);
        }

        // Update XP bar (only for player) using shared utility
        if (side === 'player') {
            const expContainer = document.getElementById('player-exp-container');
            if (expContainer) {
                updateExpBar(expContainer, pokemon);
            }
        }
    }

    /**
     * Update Pokemon indicators (6 red/gray balls)
     */
    updatePokemonIndicators() {
        if (!this.currentBattleSession) return;

        const playerIndicators = document.getElementById('player-indicators');
        const opponentIndicators = document.getElementById('opponent-indicators');

        if (playerIndicators) {
            playerIndicators.innerHTML = generatePokemonIndicators(this.currentBattleSession.playerPokemonStatus);
        }

        if (opponentIndicators) {
            opponentIndicators.innerHTML = generatePokemonIndicators(this.currentBattleSession.npcPokemonStatus);
        }
    }

    /**
     * Show battle result
     * @param {Object} result - Battle result
     */
    showBattleResult(result) {
        // Update final display
        this.updateBattleDisplay();

        // Update battle result text
        const resultTextElement = document.getElementById('battle-result-text');
        if (result.winner) {
            if (result.winner.name === 'Spieler') {
                resultTextElement.innerHTML = `<span class="battle-victory">Sieg! Du hast ${result.npcTrainer.name} besiegt!</span>`;
            } else {
                resultTextElement.innerHTML = `<span class="battle-defeat">Niederlage! ${result.winner.name} hat dich besiegt!</span>`;
            }
        } else {
            resultTextElement.innerHTML = `<span class="battle-tie-victory">Unentschieden! Beide Trainer haben keine Pokémon mehr!</span>`;
        }

        // Show continue button
        const continueBtn = document.getElementById('trainer-continue-btn');
        if (continueBtn) {
            continueBtn.style.display = 'flex';
        }

        // Log battle completion
        logger.debug(`Trainer battle completed: ${result.winner ? result.winner.name : 'Draw'} vs ${result.npcTrainer.name}`);
    }

    /**
     * Show round start animation
     * @param {number} roundNumber - Current round number
     * @param {Object} playerPokemon - Player's Pokemon
     * @param {Object} npcPokemon - NPC's Pokemon
     * @returns {Promise<void>}
     */
    async showRoundStartAnimation(roundNumber, playerPokemon, npcPokemon) {
        return new Promise((resolve) => {
            // Update battle result text to show round info
            const resultTextElement = document.getElementById('battle-result-text');
            if (resultTextElement) {
                resultTextElement.innerHTML = `<strong>Runde ${roundNumber}</strong>`;
            }

            // Reduced delay for faster gameplay (from 1000ms to 500ms)
            setTimeout(() => {
                resolve();
            }, 500);
        });
    }

    /**
     * Show battle calculation animation
     * @param {Object} battleResult - Battle calculation result
     * @returns {Promise<void>}
     */
    async showBattleCalculationAnimation(battleResult) {
        return new Promise((resolve) => {
            // Update damage values in the calculation display
            const playerDamageElement = document.getElementById('player-damage');
            const opponentDamageElement = document.getElementById('opponent-damage');

            if (playerDamageElement) {
                playerDamageElement.textContent = battleResult.playerDamage || '?';
            }
            if (opponentDamageElement) {
                opponentDamageElement.textContent = battleResult.wildDamage || '?';
            }

            // Show result after calculation (reduced from 2000ms to 1000ms)
            setTimeout(() => {
                const resultTextElement = document.getElementById('battle-result-text');
                if (resultTextElement) {
                    if (battleResult.playerWins) {
                        resultTextElement.innerHTML = `<span class="battle-victory">Spieler gewinnt!</span>`;
                    } else {
                        resultTextElement.innerHTML = `<span class="battle-defeat">Gegner gewinnt!</span>`;
                    }
                }
                resolve();
            }, 1000); // Reduced delay for faster gameplay
        });
    }

    /**
     * Show Pokemon defeat animation
     * @param {string} side - 'player' or 'opponent'
     * @returns {Promise<void>}
     */
    async showPokemonDefeatAnimation(side) {
        return new Promise((resolve) => {
            const pokemonImage = document.getElementById(`${side}-pokemon-image`);
            const healthFill = document.querySelector(`.trainer-${side}-health-fill`);
            const healthText = document.querySelector(`.trainer-${side}-card .pokemon-health-text`);

            // Animate health bar to 5% using shared utility
            if (healthFill && healthText) {
                updateHealthBar(healthFill, healthText, 5);
            }

            // Shake animation for defeated Pokemon
            if (pokemonImage) {
                pokemonImage.classList.add('pokemon-defeat-shake');
            }

            // Reduced delay for faster gameplay (from 1500ms to 1000ms)
            setTimeout(() => {
                if (pokemonImage) {
                    pokemonImage.classList.remove('pokemon-defeat-shake');
                }
                resolve();
            }, 1000);
        });
    }

    /**
     * Show Pokemon switch animation
     * @param {string} side - 'player' or 'opponent'
     * @returns {Promise<void>}
     */
    async showPokemonSwitchAnimation(side) {
        return new Promise((resolve) => {
            const pokemonImage = document.getElementById(`${side}-pokemon-image`);

            // Fade out current Pokemon
            if (pokemonImage) {
                pokemonImage.style.opacity = '0';
            }

            setTimeout(() => {
                // Update to new Pokemon and fade in
                this.updateBattleDisplay();

                if (pokemonImage) {
                    pokemonImage.style.opacity = '1';
                }

                // Reset health bar to 100% using shared utility
                const healthFill = document.querySelector(`.trainer-${side}-health-fill`);
                const healthText = document.querySelector(`.trainer-${side}-card .pokemon-health-text`);

                if (healthFill && healthText) {
                    updateHealthBar(healthFill, healthText, 100);
                }

                resolve();
            }, 500);
        });
    }

    /**
     * Award XP to player Pokemon for winning a round
     * @param {Object} playerPokemon - Player's Pokemon that won
     * @param {Object} defeatedPokemon - Defeated opponent Pokemon
     * @returns {Promise<void>}
     */
    async awardRoundXP(playerPokemon, defeatedPokemon) {
        try {
            // Calculate XP based on defeated Pokemon's level and rarity
            const expAmount = getExpFromBattle(defeatedPokemon.level || 1, defeatedPokemon.rarity || 'common');

            logger.debug(`Awarding ${expAmount} XP to ${playerPokemon.name} for defeating ${defeatedPokemon.name}`);

            // Store old level for level up detection
            const oldLevel = playerPokemon.level || 1;

            // Add experience to the Pokemon
            const expResult = await playerPokemon.addExperience(expAmount);

            // Update XP bar display using shared utility
            const expContainer = document.getElementById('player-exp-container');
            if (expContainer) {
                updateExpBar(expContainer, playerPokemon, expAmount);
            }

            // Show level up notification if Pokemon leveled up
            if (expResult.didLevelUp) {
                await this.showLevelUpNotification(playerPokemon, oldLevel, expResult.newLevel);
            }

            // Update the Pokemon in storage immediately after each round
            await pokemonManager.updatePokemon(playerPokemon);
            await pokemonManager.saveTeam();

            logger.debug(`XP awarded successfully. ${playerPokemon.name} is now level ${playerPokemon.level} with ${playerPokemon.experience} XP`);

        } catch (e) {
            logger.error('Error awarding round XP:', e);
        }
    }

    /**
     * Show level up notification
     * @param {Object} pokemon - Pokemon that leveled up
     * @param {number} oldLevel - Previous level
     * @param {number} newLevel - New level
     * @returns {Promise<void>}
     */
    async showLevelUpNotification(pokemon, oldLevel, newLevel) {
        const playerCard = document.querySelector('.trainer-player-card');
        if (playerCard) {
            const message = `${getPokemonDisplayName(pokemon)} erreicht Level ${newLevel}!`;
            await showNotification(playerCard, message, 'level-up-notification', 3000);
        }

        logger.debug(`${pokemon.name} leveled up from ${oldLevel} to ${newLevel}!`);
    }


}

// Export singleton instance
export const trainerBattleScreen = new TrainerBattleScreen();
