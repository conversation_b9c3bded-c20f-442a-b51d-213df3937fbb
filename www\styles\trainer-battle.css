/* styles/trainer-battle.css */
/* Trainer Battle Screen Styles - Based on battle-screen.css structure */

/* Battle overlay - same as BattleScreen */
.trainer-battle-overlay {
  background: var(--standard-background-color, #f4f4f4);
  position: fixed;
  top: 0; left: 0; width: 100vw; height: 100vh;
  z-index: 10020;
  overflow-y: auto;
  padding: 0;
}

#trainer-battle-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--standard-background-color, #f4f4f4);
    z-index: 1000;
    display: none;
    overflow-y: auto;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Battle container - same structure as BattleScreen */
.trainer-battle-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px);
  padding: 0;
  overflow: auto;
  background-color: #f8f8f8;
}

.trainer-battle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px 20px;
    background: var(--standard-background-color);
}

/* Battle arena - same structure as BattleScreen */
.trainer-battle-arena {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1;
  background: none;
  position: relative;
  overflow: hidden;
  height: 60vh;
}

/* Battle background image - same as BattleScreen */
.trainer-battle-background {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1 / 1;
  z-index: 0;
  object-fit: contain;
  object-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  flex-direction: row-reverse;
}

/* Battle info container - same as BattleScreen */
.trainer-battle-info-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 10px;
  position: relative;
  top: 0;
  left: 0;
  z-index: 3;
}

/* Pokemon sides - same as BattleScreen */
.trainer-player-side {
  position: relative;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 0;
  overflow: hidden;
  background: none;
}

.trainer-opponent-side {
  position: relative;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  padding: 0;
  overflow: hidden;
  background: none;
}

.trainer-battle-title {
    margin: 0;
    color: var(--standard-text-color);
}

.trainer-battle-close {
    background: var(--pinky-red);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
}

.trainer-battle-close:hover {
    background: #d63031;
    transform: scale(1.1);
}

.battle-status {
    text-align: center;
    margin-bottom: 20px;
    color: var(--standard-text-color);
    font-weight: bold;
}

.battle-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.battle-side {
    text-align: center;
    flex: 1;
}

.battle-side h3 {
    margin-top: 0;
    color: var(--standard-text-color);
}

.vs-divider {
    font-size: 24px;
    margin: 0 20px;
    color: var(--really-grey);
    font-weight: bold;
}

.pokemon-display {
    border: 2px solid var(--light-grey);
    border-radius: 10px;
    padding: 10px;
    background: white;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.pokemon-display img {
    width: 64px;
    height: 64px;
}

.pokemon-display p {
    margin: 5px 0;
    color: var(--standard-text-color);
}

.pokemon-name {
    font-weight: bold;
}

.pokemon-level {
    color: var(--really-grey);
}

.pokemon-remaining {
    font-size: 12px;
    color: var(--really-grey);
}

.pokemon-display.no-pokemon {
    background: var(--light-grey);
    color: var(--really-grey);
}

.battle-log {
    background: var(--light-grey);
    border-radius: 10px;
    padding: 15px;
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.battle-log h4 {
    margin-top: 0;
    color: var(--standard-text-color);
}

.battle-log-entry {
    margin-bottom: 5px;
    padding: 5px;
    background: white;
    border-radius: 5px;
    font-size: 14px;
    color: var(--standard-text-color);
}

.battle-result {
    display: none;
    text-align: center;
    margin-top: 20px;
}

.result-title {
    margin: 0 0 10px 0;
}

.result-title.victory {
    color: var(--green);
}

.result-title.defeat {
    color: var(--pinky-red);
}

.result-title.draw {
    color: var(--orange);
}

.result-description {
    color: var(--standard-text-color);
    margin-bottom: 15px;
}

.battle-finish-btn {
    background: var(--green);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.battle-finish-btn:hover {
    background: #00b894;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.battle-finish-btn:active {
    transform: translateY(0);
}

.pokemon-type-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    color: white;
    font-size: 12px;
    font-weight: bold;
    margin: 2px;
}

/* Trainer marker styles */
.trainer-marker {
    border: 2px solid var(--orange);
    border-radius: 50%;
    background: white;
}

.trainer-popup {
    min-width: 200px;
}

.trainer-popup .team-list {
    max-height: 150px;
    overflow-y: auto;
    margin: 5px 0;
}

.challenge-button {
    background: var(--main-blue);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.challenge-button:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.challenge-button:disabled {
    background: var(--really-grey);
    cursor: not-allowed;
    transform: none;
}

.team-requirement-warning {
    color: var(--pinky-red);
    font-size: 12px;
    margin-top: 5px;
    font-style: italic;
}

/* Battle Intro Styles - Removed as intro screen is no longer used */

/* Pokemon battle cards - same as BattleScreen */
.pokemon-battle-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 8px;
  width: 160px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  z-index: 2;
  margin: 0 5px;
  position: relative;
  height: 100%;
  flex: 1;
  max-width: 280px;
}

.trainer-player-card {
  align-self: flex-start;
}

.trainer-opponent-card {
  align-self: flex-end;
}

.pokemon-battle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.pokemon-battle-name {
  font-weight: 600;
  color: var(--standard-text-color);
  font-size: 0.8rem;
}

.pokemon-battle-level {
  font-size: 0.7rem;
  color: #666;
  margin-left: 5px;
}

/* Health bar styles - same as BattleScreen */
.pokemon-health-container {
  margin: 5px 0 8px 0;
}

.pokemon-health-bar {
  height: 10px;
  background-color: #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 2px;
}

.pokemon-health-fill {
  height: 100%;
  background-color: #4CAF50;
  border-radius: 5px;
  transition: width 1s ease-in-out, background-color 0.5s ease-in-out;
}

.low-health {
  background-color: #f44336 !important;
}

.pokemon-health-text {
  font-size: 0.7rem;
  color: #666;
  text-align: right;
}

/* Experience bar styles - same as BattleScreen */
.pokemon-exp-container {
  margin: 2px 0;
  width: 100%;
}

.pokemon-exp-bar {
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 2px;
  position: relative;
}

.pokemon-exp-fill {
  height: 100%;
  background-color: #4a90e2;
  border-radius: 3px;
  transition: width 0.5s ease-out;
  position: relative;
  z-index: 1;
}

.pokemon-exp-new {
  height: 100%;
  background-color: #4a90e2;
  border-radius: 3px;
  opacity: 0;
  position: absolute;
  top: 0;
  bottom: 0;
  transition: opacity 0.3s ease-in;
  z-index: 2;
}

.pokemon-exp-text {
  font-size: 0.7rem;
  color: #666;
  text-align: right;
}

/* Level up notification - same as BattleScreen */
.level-up-notification {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 215, 0, 0.9);
  color: #333;
  font-weight: bold;
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.7);
  animation: levelUpGlow 1.5s infinite;
  opacity: 0;
  z-index: 10;
  transition: opacity 0.5s ease-in;
}

@keyframes levelUpGlow {
  0% {
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.7);
  }
  50% {
    box-shadow: 0 0 15px rgba(255, 215, 0, 1);
  }
  100% {
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.7);
  }
}

/* Pokemon battle types - same as BattleScreen */
.pokemon-battle-types {
  display: flex;
  gap: 3px;
  margin-top: 5px;
  flex-wrap: wrap;
  align-items: center;
}

.pokemon-type-badge-group {
  display: flex;
  align-items: center;
  margin-right: 5px;
  margin-bottom: 3px;
}

.pokemon-type-badge-group > .pokemon-type-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  border-radius: 12px;
  color: white;
  font-size: 0.6rem;
  font-weight: 500;
  text-shadow: 0 1px 1px rgba(0,0,0,0.3);
  box-shadow: 0 1px 2px rgba(0,0,0,0.2);
  margin-right: 2px;
}

/* Type effectiveness styles - same as BattleScreen */
.type-effectiveness {
  display: inline-block;
  font-size: 0.6rem;
  padding: 1px 3px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.8);
  font-weight: bold;
  margin-right: 2px;
  margin-left: 2px;
}

.type-super {
  color: #2e7d32;
  background-color: rgba(46, 125, 50, 0.2);
}

.type-not-very {
  color: #c62828;
  background-color: rgba(198, 40, 40, 0.2);
}

.type-no-effect {
  color: #424242;
  background-color: rgba(66, 66, 66, 0.2);
}

.type-neutral {
  color: #1976d2;
  background-color: rgba(25, 118, 210, 0.2);
}

/* Pokemon battle images - same as BattleScreen */
.pokemon-battle-image {
  width: auto;
  height: auto;
  object-fit: contain;
  transition: all 0.3s ease;
  max-width: 90%;
  position: relative;
  z-index: 1;
}

.trainer-player-pokemon {
  position: absolute;
  left: 25%;
  bottom: 18%;
  transform: scaleX(-1);
}

.trainer-opponent-pokemon {
  position: absolute;
  right: 20%;
  bottom: 34%;
}

/* 6-Pokemon Indicator System */
.pokemon-indicators {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin: 10px 0;
  position: absolute;
  z-index: 4;
}

.pokemon-indicators.player-indicators {
  bottom: 10px;
  left: 10px;
}

.pokemon-indicators.opponent-indicators {
  top: 10px;
  right: 10px;
}

.pokemon-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ff4444; /* Red for active Pokemon */
  border: 1px solid #333;
  transition: background-color 0.3s ease;
}

.pokemon-indicator.defeated {
  background-color: #888; /* Gray for defeated Pokemon */
}

/* Battle results - same as BattleScreen */
.trainer-battle-results {
  background-color: white;
  padding: 8px;
  text-align: center;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
  z-index: 5;
  font-size: 1rem;
  font-weight: 500;
  min-height: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 5px;
  border-radius: 10px;
}

/* Battle calculation styles - same as BattleScreen */
.trainer-battle-calculation {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  font-size: 0.9rem;
  color: #666;
  opacity: 0;
  animation: fadeIn 0.5s ease-out 2.3s forwards;
}

.trainer-battle-calculation-vs {
  margin: 0 10px;
  font-weight: bold;
  animation: pulse 1.5s infinite 2.5s;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.trainer-player-level, .trainer-opponent-level {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  font-weight: bold;
  color: white;
  opacity: 0;
  transform: scale(0);
}

.trainer-player-level {
  background-color: #f14b3d;
  animation: popIn 0.4s ease-out 2.5s forwards;
}

.trainer-opponent-level {
  background-color: #595f65;
  animation: popIn 0.4s ease-out 2.7s forwards;
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  70% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Continue FAB button - same as BattleScreen */
.trainer-continue-fab {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: var(--button-color, #4CAF50);
  color: white;
  border: none;
  cursor: pointer;
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10030;
  opacity: 0;
  animation: fadeIn 0.5s ease-out 3s forwards;
}

.trainer-continue-fab img {
  width: 24px;
  height: 24px;
}

.trainer-continue-fab:hover {
  background-color: var(--button-hover-color, #45a049);
}

.trainer-continue-fab:active {
  transform: translateY(2px);
  box-shadow: 0 1px 3px rgba(0,0,0,0.16), 0 1px 3px rgba(0,0,0,0.23);
}

/* Media queries for responsive design - same as BattleScreen */
@media screen and (orientation: landscape) {
  .trainer-battle-container {
    flex-direction: column;
  }

  .trainer-battle-arena {
    height: 40vh;
  }

  .trainer-battle-results {
    flex: 1;
    order: 2;
    position: static;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    min-height: 0;
  }

  .trainer-player-pokemon {
    left: 15%;
    top: 15%;
  }

  .trainer-opponent-pokemon {
    right: 15%;
    bottom: 15%;
  }

  .pokemon-battle-card {
    max-width: 160px;
  }
}

@media screen and (max-height: 500px) {
  .pokemon-battle-image {
    width: 60px;
    height: 60px;
  }

  .trainer-battle-arena {
    height: 50vh;
  }

  .trainer-player-pokemon {
    left: 5%;
    top: 5%;
  }

  .trainer-opponent-pokemon {
    right: 5%;
    bottom: 5%;
  }

  .pokemon-battle-card {
    width: 150px;
    padding: 5px;
  }

  .trainer-battle-results {
    min-height: 50px;
    padding: 5px;
    font-size: 0.9rem;
  }
}

/* Trainer intro animation styles */
.trainer-intro-sprite {
  position: absolute;
  width: 120px;
  height: auto;
  z-index: 5;
  animation: fadeIn 0.5s ease-in;
}

.player-trainer-sprite {
  left: 25%;
  bottom: 20%;
}

.opponent-trainer-sprite {
  right: 25%;
  bottom: 20%;
}

.trainer-intro-name {
  position: absolute;
  z-index: 6;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 5px;
  font-weight: bold;
  font-size: 0.9rem;
  animation: fadeIn 0.5s ease-in;
}

.player-trainer-name {
  left: 25%;
  bottom: 10%;
}

.opponent-trainer-name {
  right: 25%;
  bottom: 10%;
}

/* Exit animations */
.slide-left-exit {
  animation: slideLeftExit 0.5s ease-in forwards;
}

.slide-right-exit {
  animation: slideRightExit 0.5s ease-in forwards;
}

.fade-out {
  animation: fadeOut 0.5s ease-in forwards;
}

@keyframes slideLeftExit {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes slideRightExit {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* Pokemon defeat animation */
.pokemon-defeat-shake {
  animation: pokemonDefeatShake 0.5s ease-in-out;
}

@keyframes pokemonDefeatShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* Battle result text styling */
.battle-victory {
  color: #4CAF50;
  font-weight: bold;
}

.battle-defeat {
  color: #f44336;
  font-weight: bold;
}

.battle-tie-victory {
  color: #FF9800;
  font-weight: bold;
}

/* Trainer intro screen styling - Removed as intro screen is no longer used */

/* Battle Main Styles */
.battle-main {
    /* Inherits existing styles */
}
